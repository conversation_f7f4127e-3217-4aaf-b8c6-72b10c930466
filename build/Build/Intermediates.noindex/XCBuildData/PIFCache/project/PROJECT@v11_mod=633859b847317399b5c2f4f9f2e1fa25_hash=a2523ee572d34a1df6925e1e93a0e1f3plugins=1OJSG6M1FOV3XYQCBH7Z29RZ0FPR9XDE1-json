{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "SDKROOT": "iphoneos", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG $(inherited)", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>"}, "guid": "a2523ee572d34a1df6925e1e93a0e1f39ad37cdb5f8b2c679e9aafcf756094fd", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++20", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_USER_SCRIPT_SANDBOXING": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu17", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "18.4", "LOCALIZATION_PREFERS_STRING_CATALOGS": "YES", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "SDKROOT": "iphoneos", "SWIFT_COMPILATION_MODE": "wholemodule", "VALIDATE_PRODUCT": "YES"}, "guid": "a2523ee572d34a1df6925e1e93a0e1f3de90b14f1769473594a6179eb0d393b1", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c6eac39e086c96ca67c5be439dfee1c9", "path": "Color+Extensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f35ba4c99d3bbb3a8ba435ddb82d50b9dc", "path": "String+Localization.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f370ad33993979d3056c3700cf85a0c4c8", "name": "Extensions", "path": "Extensions", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d8f6592e08074f6cdd018e355af88ee1", "path": "爸爸头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f35dbf224cec9a42f6a4cf1825ba3145ba", "path": "宝箱未打开.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e98694c8a719dc3bc5b58878d3a27b60", "path": "宝箱已打开.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3db5d6ee2229b18d6257233b3283576bf", "path": "初级会员.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f381374e2dd899aaecf31161a7b3378412", "path": "道具配置.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f30f6e0ac075d3396b9e185c9797128df0", "path": "登录页面logo.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f38c15464655a35321226a736c6eb48fa9", "path": "高级会员.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f38db79a294712d8cf68ff7781eac2b0cd", "path": "个人中心插图.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a155ef14b06278ce87ac00e2fa124d4c", "path": "刮刮卡.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c84d6ec53728cd2591268cbd630a726f", "path": "刮刮卡中奖.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f38d676a7752d0fd0af01c51c00593239e", "path": "关闭.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3601c4702972dc2ea63204e5cfb4f3607", "path": "皇冠.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f31c465dd9e535af1c1fd3e4a546892b44", "path": "皇冠(订阅）.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3bd230dacfbb76fc7cbad8d24e14b07b7", "path": "进入.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f320b8eb8c9fe00d0df0814f5710b1fe18", "path": "录音.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d399ad9ce681eca4a3c601e2a96b2180", "path": "妈妈头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a42a34e0fb92713c6b2b713023768fc3", "path": "男生头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3673be8878f4fcb1401337581b3672af0", "path": "女生头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f30d51ca7ec0648a9a1611624f10f8a9b3", "path": "其他头像.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3202b565e397f71663806c8dfd328f548", "path": "全班操作.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f38eedbdd0b715b1982fede0b9bd2afd6a", "path": "添加学生.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.jpeg", "guid": "a2523ee572d34a1df6925e1e93a0e1f36f9fd4393e81acc8149d3a5b610b92a8", "path": "团团转logo.jpg", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e38c53ad428b7aa353ad83e415b74bf2", "path": "烟花.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f32356e483a3860b484cf50584313b9de8", "path": "banjixinxi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f30e893f1339114a855a0455592afa9d22", "path": "chengzhang.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b0736a73747ff4cca897daa96d92907c", "path": "choujiang.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3bc83579b0f3466ffd6a0b7b582f64942", "path": "denglu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f356f81c60cd36018dd14a1db69a2bc3d4", "path": "fanhui.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3ef42f2f22387f9539478aa321406bb77", "path": "fenxi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3afa5c431d952ba500a970a172d08946a", "path": "guanyu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f72df8017ede72867bc5cac9bb45d365", "path": "guizepeizhi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a6b16de708b2ec4f0fddae5f7663f531", "path": "guzhangfankui.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3bc24379dccf32a855b968e4af8367bea", "path": "huiyuan.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3bdcb1284b0692d04bb1880da1022dc06", "path": "jianpan.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f31980cb16a6e03398dbd630376b322751", "path": "laoshi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f38de8370bb33b818d540889a3abed9fbb", "path": "lingqujilu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e6a77a4f5be110ffcb7de6348367793a", "path": "lishi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3458ebceae5d25de9e4def76ce4145343", "path": "logo.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3ca93ea23bc0270b54a58ec4e24a2317a", "path": "shanchu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f38d4a644e80736bcba8e5a80858baee42", "path": "shouye1_1.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f351e13c3038bf0f0d228ebdd1b545262d", "path": "sousuo.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b2fd9a7d661babe4512775e799ded71c", "path": "tuichu.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f334fa4dfdebe79873cfab08ff9c9eee43", "path": "wode13.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3cc84705f9ee6b974795cc0b66ebdbcb0", "path": "yinsizhengce.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d298ea54367b33e9f2290cbbe687cc15", "path": "yonghuxieyi.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "a2523ee572d34a1df6925e1e93a0e1f3b67464d628c8161fcde8b21e6eb0d05e", "path": "yuyan.png", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f392e36a7082050c3789f32049665071bf", "name": "image", "path": "image", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3fce276a9b86ee2179ca073e1a94e8f65", "path": "CoreDataExtensions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f30170d6514d889dd6f5b76f190d56a775", "path": "DataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c61f91cf76b40a4768637da92ebbdad0", "path": "DataModelUsageExamples.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38ca85d4df71953623420f91bc7a0ea1f", "path": "MemberPointsModels.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f395ebe2ba93a96f05becddf21ec248c15", "name": "Models", "path": "Models", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f342b3e73742c341db1c2c1b34ccae2877", "path": "DesignSystem.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3803e3c53c6f7e08d5cbcad8a81ba7d7e", "path": "ResponsiveLayoutConfig.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3f129bb153b070ccefcbd37c957820a09", "name": "Styles", "path": "Styles", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3017e2b513fe11a1c5f606bc883ae4446", "path": "GrowthDiaryViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f306cf60aac4329f2234affe83d95b1865", "path": "HomeViewModel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3963e588879f383e55f8171795ac3b9d3", "path": "MemberDetailViewModel.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3d630cbeccd6663d06a063b53d76bbc4f", "name": "ViewModels", "path": "ViewModels", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f301cc7e21cfdd030e25579f546381145f", "path": "ActionButtonsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d075cb645c7e37ef47e1619fbefd4009", "path": "AddMemberFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d2ded1dcb90d4eb7dc8c37a5dc4b3989", "path": "AddRewardFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3330669ce08c7b88bc929e60cda770cc3", "path": "CustomRewardExchangeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f33379bcce17f5adbde0a907c87d59b00f", "path": "CustomTabBar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f30772f5e19ef3f56fee10ec6c2ae7af18", "path": "DatePickerPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f374a4f788b49b13eeceb723c8dfdcf969", "path": "DatePickerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f349d4d7389438d3c10c0f214970b4fbb6", "path": "DateRangePickerView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3c20b48a65fb4de987ee26772b0d7ee81", "path": "FamilyMemberCardView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3d5b488c4c714e6d7b66d9b4fe71f5544", "path": "FamilyMemberGridView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f310afc518a9796d5bc39488071417f27d", "path": "FamilyOperationFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f34e7c92a9d8cd982448e51c034fb10c4f", "path": "FamilyOperationOptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f36e54c3e2c8e87e670043e903ce08b43f", "path": "FamilyTotalScoreView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f39a8b07f75acecba5bd2af78296e1347b", "path": "LiquidTabBarBackground.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38f08a9570ab3fbb875754bce21a6cdbc", "path": "LiquidTabBarBubble.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f33d45ce37f52753e3e04a33cf26e7da8d", "path": "LiquidTabBarShape.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f368ef3bf18d4c86c7b322564e76e90b90", "path": "MemberPickerPopupView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f333acb6e2eb2bf3a526a65d5f3ec124c8", "path": "MemberPointsFormView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f376abb6007b4b20909a93667057096c8c", "path": "MemberPointsOptionsView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f31821721b8fd407e8ef19bcc1dedb89c7", "path": "MemberRewardExchangeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3693c04a161c5d2b18d971d780a326545", "path": "RoleSelectionView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3ce8b525b4dd20569bbb62aefd493b9f3", "name": "Components", "path": "Components", "sourceTree": "<group>", "type": "group"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f355170a641ed509b9843fd2c2a0a78622", "path": "CardComparisonTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f37d0b2ce47aaa5aa6e5751b10adfb1565", "path": "GrowthDiaryView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38206324ed9b8e3b9056d277bc502210c", "path": "HomeView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f38f78d7ab2116f7c7216e41551a41c72c", "path": "MainTabView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f32b84f27ea6756ae108fdbe59c8b4a0d3", "path": "MemberDetailTestView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3f45cf29e7f6067d6afe3a5099429869d", "path": "MemberDetailView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3693c6bc32f40a0f52c2333f0f41c489d", "path": "ProfileView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f37d89a8bac75577247be879f55f0ca649", "path": "SubscriptionView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3c26a3610856929fae953c2329b4a38ef", "name": "Views", "path": "Views", "sourceTree": "<group>", "type": "group"}, {"fileType": "net.daringfireball.markdown", "guid": "a2523ee572d34a1df6925e1e93a0e1f31b53c0ca14a57f6812d6802898cb62a3", "path": "成员卡片样式优化总结.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "a2523ee572d34a1df6925e1e93a0e1f300601b4fed8a907c9fc92386fd03463e", "path": "Assets.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f359833e58b8d84bcd2be11818112de5ab", "path": "ContentView.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.plist.strings", "guid": "a2523ee572d34a1df6925e1e93a0e1f3cfcef677bab8dd56d264b0e506d89d1f", "path": "zh-Hans.lproj/Localizable.strings", "regionVariantName": "zh-Hans", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3ba0f57bd1dd8f01090103222e33df4b9", "name": "Localizable.strings", "path": "", "sourceTree": "<group>", "type": "variantGroup"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f357a12addcfe000dd5b6ef3df4068eb4b", "path": "Persistence.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "wrapper.xcdatamodel", "guid": "a2523ee572d34a1df6925e1e93a0e1f380cdc9904a18a4c5b288dba143aed0ba", "path": "ztt2.xcdatamodel", "sourceTree": "<group>", "type": "file"}], "fileType": "wrapper.xcdatamodeld", "guid": "a2523ee572d34a1df6925e1e93a0e1f3e29b48fa4a0353c0f2c545e18cc6008e", "name": "ztt2.xcdatamodeld", "path": "ztt2.xcdatamodeld", "sourceTree": "<group>", "type": "versionGroup"}, {"fileType": "sourcecode.swift", "guid": "a2523ee572d34a1df6925e1e93a0e1f3a32ac91dd6c67eb92e89b310b316ee7d", "path": "ztt2App.swift", "sourceTree": "<group>", "type": "file"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f3f29ab29fce8e6a565e14563d07291ced", "name": "ztt2", "path": "ztt2", "sourceTree": "<group>", "type": "group"}, {"guid": "a2523ee572d34a1df6925e1e93a0e1f3ac5bcebb2167034ea4e1f5470b4c2e93", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "a2523ee572d34a1df6925e1e93a0e1f36944541524b1a644404d2e856465bfd1", "name": "ztt2", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "a2523ee572d34a1df6925e1e93a0e1f3", "path": "/Users/<USER>/Desktop/我的项目/转团团/ztt2/ztt2.xcodeproj", "projectDirectory": "/Users/<USER>/Desktop/我的项目/转团团/ztt2", "targets": ["TARGET@v11_hash=708d446fdfcf79799f98e5e1e274808a"]}