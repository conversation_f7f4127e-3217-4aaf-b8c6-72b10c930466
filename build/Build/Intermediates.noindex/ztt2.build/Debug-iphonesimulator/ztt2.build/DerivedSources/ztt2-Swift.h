#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 6.1 effective-5.10 (swiftlang-6.1.0.110.21 clang-1700.0.13.3)
#ifndef ZTT2_SWIFT_H
#define ZTT2_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef unsigned char char8_t;
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import CoreData;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="ztt2",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

@class NSEntityDescription;
@class NSManagedObjectContext;
SWIFT_CLASS_NAMED("AIReport")
@interface AIReport : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@class NSUUID;
@class NSString;
@class NSDate;
@class Member;
@interface AIReport (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable title;
@property (nonatomic, copy) NSString * _Nullable content;
@property (nonatomic, copy) NSString * _Nullable reportType;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, copy) NSString * _Nullable inputDataSummary;
@property (nonatomic) int32_t totalRecords;
@property (nonatomic) int32_t positiveRecords;
@property (nonatomic) int32_t negativeRecords;
@property (nonatomic, strong) Member * _Nullable member;
@end

SWIFT_CLASS_NAMED("DiaryEntry")
@interface DiaryEntry : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface DiaryEntry (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable content;
@property (nonatomic, copy) NSDate * _Nullable timestamp;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, copy) NSDate * _Nullable updatedAt;
@property (nonatomic, strong) Member * _Nullable member;
@end

SWIFT_CLASS_NAMED("GlobalRule")
@interface GlobalRule : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@class User;
@interface GlobalRule (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable name;
@property (nonatomic) int32_t value;
@property (nonatomic, copy) NSString * _Nullable type;
@property (nonatomic) BOOL isFrequent;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, strong) User * _Nullable user;
@end

SWIFT_CLASS_NAMED("LotteryConfig")
@interface LotteryConfig : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@class LotteryItem;
@class NSSet;
@interface LotteryConfig (SWIFT_EXTENSION(ztt2))
- (void)addItemsObject:(LotteryItem * _Nonnull)value;
- (void)removeItemsObject:(LotteryItem * _Nonnull)value;
- (void)addItems:(NSSet * _Nonnull)values;
- (void)removeItems:(NSSet * _Nonnull)values;
@end

@interface LotteryConfig (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable toolType;
@property (nonatomic) int32_t itemCount;
@property (nonatomic) int32_t costPerPlay;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, copy) NSDate * _Nullable updatedAt;
@property (nonatomic, strong) Member * _Nullable member;
@property (nonatomic, strong) NSSet * _Nullable items;
@end

SWIFT_CLASS_NAMED("LotteryItem")
@interface LotteryItem : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface LotteryItem (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic) int32_t itemIndex;
@property (nonatomic, copy) NSString * _Nullable prizeName;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, strong) LotteryConfig * _Nullable lotteryConfig;
@end

SWIFT_CLASS_NAMED("LotteryRecord")
@interface LotteryRecord : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface LotteryRecord (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable toolType;
@property (nonatomic, copy) NSString * _Nullable prizeResult;
@property (nonatomic) int32_t cost;
@property (nonatomic, copy) NSDate * _Nullable timestamp;
@property (nonatomic, strong) Member * _Nullable member;
@end

SWIFT_CLASS_NAMED("Member")
@interface Member : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface Member (SWIFT_EXTENSION(ztt2))
- (void)addLotteryConfigsObject:(LotteryConfig * _Nonnull)value;
- (void)removeLotteryConfigsObject:(LotteryConfig * _Nonnull)value;
- (void)addLotteryConfigs:(NSSet * _Nonnull)values;
- (void)removeLotteryConfigs:(NSSet * _Nonnull)values;
@end

@class MemberPrize;
@interface Member (SWIFT_EXTENSION(ztt2))
- (void)addMemberPrizesObject:(MemberPrize * _Nonnull)value;
- (void)removeMemberPrizesObject:(MemberPrize * _Nonnull)value;
- (void)addMemberPrizes:(NSSet * _Nonnull)values;
- (void)removeMemberPrizes:(NSSet * _Nonnull)values;
@end

@interface Member (SWIFT_EXTENSION(ztt2))
- (void)addAiReportsObject:(AIReport * _Nonnull)value;
- (void)removeAiReportsObject:(AIReport * _Nonnull)value;
- (void)addAiReports:(NSSet * _Nonnull)values;
- (void)removeAiReports:(NSSet * _Nonnull)values;
@end

@interface Member (SWIFT_EXTENSION(ztt2))
- (void)addLotteryRecordsObject:(LotteryRecord * _Nonnull)value;
- (void)removeLotteryRecordsObject:(LotteryRecord * _Nonnull)value;
- (void)addLotteryRecords:(NSSet * _Nonnull)values;
- (void)removeLotteryRecords:(NSSet * _Nonnull)values;
@end

@class RedemptionRecord;
@interface Member (SWIFT_EXTENSION(ztt2))
- (void)addRedemptionRecordsObject:(RedemptionRecord * _Nonnull)value;
- (void)removeRedemptionRecordsObject:(RedemptionRecord * _Nonnull)value;
- (void)addRedemptionRecords:(NSSet * _Nonnull)values;
- (void)removeRedemptionRecords:(NSSet * _Nonnull)values;
@end

@interface Member (SWIFT_EXTENSION(ztt2))
- (void)addDiaryEntriesObject:(DiaryEntry * _Nonnull)value;
- (void)removeDiaryEntriesObject:(DiaryEntry * _Nonnull)value;
- (void)addDiaryEntries:(NSSet * _Nonnull)values;
- (void)removeDiaryEntries:(NSSet * _Nonnull)values;
@end

@class PointRecord;
@interface Member (SWIFT_EXTENSION(ztt2))
- (void)addPointRecordsObject:(PointRecord * _Nonnull)value;
- (void)removePointRecordsObject:(PointRecord * _Nonnull)value;
- (void)addPointRecords:(NSSet * _Nonnull)values;
- (void)removePointRecords:(NSSet * _Nonnull)values;
@end

@class MemberRule;
@interface Member (SWIFT_EXTENSION(ztt2))
- (void)addMemberRulesObject:(MemberRule * _Nonnull)value;
- (void)removeMemberRulesObject:(MemberRule * _Nonnull)value;
- (void)addMemberRules:(NSSet * _Nonnull)values;
- (void)removeMemberRules:(NSSet * _Nonnull)values;
@end

@interface Member (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable name;
@property (nonatomic, copy) NSString * _Nullable role;
@property (nonatomic, copy) NSDate * _Nullable birthDate;
@property (nonatomic) int32_t memberNumber;
@property (nonatomic) int32_t currentPoints;
@property (nonatomic, copy) NSString * _Nullable avatar;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, copy) NSDate * _Nullable updatedAt;
@property (nonatomic, strong) User * _Nullable user;
@property (nonatomic, strong) NSSet * _Nullable pointRecords;
@property (nonatomic, strong) NSSet * _Nullable diaryEntries;
@property (nonatomic, strong) NSSet * _Nullable redemptionRecords;
@property (nonatomic, strong) NSSet * _Nullable lotteryRecords;
@property (nonatomic, strong) NSSet * _Nullable aiReports;
@property (nonatomic, strong) NSSet * _Nullable memberRules;
@property (nonatomic, strong) NSSet * _Nullable memberPrizes;
@property (nonatomic, strong) NSSet * _Nullable lotteryConfigs;
@end

SWIFT_CLASS_NAMED("MemberPrize")
@interface MemberPrize : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface MemberPrize (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable name;
@property (nonatomic) int32_t cost;
@property (nonatomic, copy) NSString * _Nullable type;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, strong) Member * _Nullable member;
@end

SWIFT_CLASS_NAMED("MemberRule")
@interface MemberRule : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface MemberRule (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable name;
@property (nonatomic) int32_t value;
@property (nonatomic, copy) NSString * _Nullable type;
@property (nonatomic) BOOL isFrequent;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, strong) Member * _Nullable member;
@end

SWIFT_CLASS_NAMED("PointRecord")
@interface PointRecord : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface PointRecord (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable reason;
@property (nonatomic) int32_t value;
@property (nonatomic, copy) NSDate * _Nullable timestamp;
@property (nonatomic, copy) NSString * _Nullable recordType;
@property (nonatomic) BOOL isReversed;
@property (nonatomic, strong) Member * _Nullable member;
@end

SWIFT_CLASS_NAMED("RedemptionRecord")
@interface RedemptionRecord : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface RedemptionRecord (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable prizeName;
@property (nonatomic) int32_t cost;
@property (nonatomic, copy) NSDate * _Nullable timestamp;
@property (nonatomic, copy) NSString * _Nullable source;
@property (nonatomic, strong) Member * _Nullable member;
@end

SWIFT_CLASS_NAMED("Subscription")
@interface Subscription : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface Subscription (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable subscriptionType;
@property (nonatomic) BOOL isActive;
@property (nonatomic, copy) NSDate * _Nullable startDate;
@property (nonatomic, copy) NSDate * _Nullable endDate;
@property (nonatomic, copy) NSString * _Nullable productIdentifier;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, copy) NSDate * _Nullable updatedAt;
@property (nonatomic, strong) User * _Nullable user;
@end

SWIFT_CLASS_NAMED("User")
@interface User : NSManagedObject
- (nonnull instancetype)initWithEntity:(NSEntityDescription * _Nonnull)entity insertIntoManagedObjectContext:(NSManagedObjectContext * _Nullable)context OBJC_DESIGNATED_INITIALIZER;
@end

@interface User (SWIFT_EXTENSION(ztt2))
- (void)addGlobalRulesObject:(GlobalRule * _Nonnull)value;
- (void)removeGlobalRulesObject:(GlobalRule * _Nonnull)value;
- (void)addGlobalRules:(NSSet * _Nonnull)values;
- (void)removeGlobalRules:(NSSet * _Nonnull)values;
@end

@interface User (SWIFT_EXTENSION(ztt2))
- (void)addMembersObject:(Member * _Nonnull)value;
- (void)removeMembersObject:(Member * _Nonnull)value;
- (void)addMembers:(NSSet * _Nonnull)values;
- (void)removeMembers:(NSSet * _Nonnull)values;
@end

@interface User (SWIFT_EXTENSION(ztt2))
@property (nonatomic, copy) NSUUID * _Nullable id;
@property (nonatomic, copy) NSString * _Nullable nickname;
@property (nonatomic, copy) NSString * _Nullable email;
@property (nonatomic, copy) NSString * _Nullable appleUserID;
@property (nonatomic, copy) NSDate * _Nullable createdAt;
@property (nonatomic, strong) Subscription * _Nullable subscription;
@property (nonatomic, strong) NSSet * _Nullable members;
@property (nonatomic, strong) NSSet * _Nullable globalRules;
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
